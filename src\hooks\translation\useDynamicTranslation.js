import { useState, useEffect, useCallback } from 'react';
import { useTranslationContext } from '../../contexts/TranslationContext';

// LibreTranslate API configuration
const LIBRETRANSLATE_API_URL = 'https://libretranslate.com/translate'; // Free public instance
// Alternative: 'http://localhost:5000/translate' if you want to self-host
// Fallback URLs in case the primary one fails
const FALLBACK_URLS = [
  'https://translate.argosopentech.com/translate',
  'https://libretranslate.de/translate'
];

// Language code mapping for LibreTranslate
const LANGUAGE_CODE_MAP = {
  en: 'en',
  es: 'es', 
  fr: 'fr',
  lv: 'lv',
  pl: 'pl'
};

export const useDynamicTranslation = () => {
  const { currentLanguage } = useTranslationContext();
  const [translationCache, setTranslationCache] = useState(new Map());
  const [isTranslating, setIsTranslating] = useState(false);

  // Function to translate text using LibreTranslate with fallback
  const translateText = useCallback(async (text, targetLanguage = currentLanguage, sourceLanguage = 'en') => {
    // Return original text if target language is English or same as source
    if (targetLanguage === 'en' || targetLanguage === sourceLanguage) {
      return text;
    }

    // Check cache first
    const cacheKey = `${text}_${sourceLanguage}_${targetLanguage}`;
    if (translationCache.has(cacheKey)) {
      return translationCache.get(cacheKey);
    }

    // Skip translation for empty or very short text
    if (!text || text.trim().length < 2) {
      return text;
    }

    setIsTranslating(true);

    // Try multiple translation services
    const urlsToTry = [LIBRETRANSLATE_API_URL, ...FALLBACK_URLS];

    for (let i = 0; i < urlsToTry.length; i++) {
      try {
        const response = await fetch(urlsToTry[i], {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            q: text,
            source: LANGUAGE_CODE_MAP[sourceLanguage] || sourceLanguage,
            target: LANGUAGE_CODE_MAP[targetLanguage] || targetLanguage,
            format: 'text'
          }),
        });

        if (!response.ok) {
          throw new Error(`Translation failed: ${response.status}`);
        }

        const data = await response.json();
        const translatedText = data.translatedText || text;

        // Cache the translation
        setTranslationCache(prev => new Map(prev.set(cacheKey, translatedText)));

        setIsTranslating(false);
        return translatedText;
      } catch (error) {
        console.warn(`Translation attempt ${i + 1} failed:`, error);
        // Continue to next URL
      }
    }

    // If all translation attempts failed, return original text
    console.error('All translation services failed');
    setIsTranslating(false);
    return text;
  }, [currentLanguage, translationCache]);

  // Function to translate multiple texts at once
  const translateTexts = useCallback(async (texts, targetLanguage = currentLanguage, sourceLanguage = 'en') => {
    const translations = await Promise.all(
      texts.map(text => translateText(text, targetLanguage, sourceLanguage))
    );
    return translations;
  }, [translateText, currentLanguage]);

  // Function to translate object properties
  const translateObject = useCallback(async (obj, fieldsToTranslate = [], targetLanguage = currentLanguage, sourceLanguage = 'en') => {
    if (!obj || typeof obj !== 'object') {
      return obj;
    }

    const translatedObj = { ...obj };

    for (const field of fieldsToTranslate) {
      if (obj[field] && typeof obj[field] === 'string') {
        translatedObj[field] = await translateText(obj[field], targetLanguage, sourceLanguage);
      }
    }

    return translatedObj;
  }, [translateText, currentLanguage]);

  // Function to translate array of objects
  const translateObjectArray = useCallback(async (array, fieldsToTranslate = [], targetLanguage = currentLanguage, sourceLanguage = 'en') => {
    if (!Array.isArray(array)) {
      return array;
    }

    const translatedArray = await Promise.all(
      array.map(item => translateObject(item, fieldsToTranslate, targetLanguage, sourceLanguage))
    );

    return translatedArray;
  }, [translateObject, currentLanguage]);

  // Clear cache when language changes
  useEffect(() => {
    setTranslationCache(new Map());
  }, [currentLanguage]);

  return {
    translateText,
    translateTexts,
    translateObject,
    translateObjectArray,
    isTranslating,
    currentLanguage,
    clearCache: () => setTranslationCache(new Map()),
  };
};
