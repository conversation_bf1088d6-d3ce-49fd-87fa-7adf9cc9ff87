import React, { createContext, useContext, useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';

const TranslationContext = createContext();

export const useTranslationContext = () => {
  const context = useContext(TranslationContext);
  if (!context) {
    throw new Error('useTranslationContext must be used within a TranslationProvider');
  }
  return context;
};

export const TranslationProvider = ({ children }) => {
  const { i18n } = useTranslation();
  const [currentLanguage, setCurrentLanguage] = useState(i18n.language || 'en');
  const [isTranslating, setIsTranslating] = useState(false);

  // Language mapping for display names
  const languageMap = {
    en: { code: 'en', name: 'English (US)', flag: '🇺🇸' },
    es: { code: 'es', name: 'Español', flag: '🇪🇸' },
    fr: { code: 'fr', name: 'Fran<PERSON>', flag: '🇫🇷' },
    lv: { code: 'lv', name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', flag: '🇱🇻' },
    pl: { code: 'pl', name: '<PERSON><PERSON>', flag: '🇵🇱' },
  };

  const supportedLanguages = Object.keys(languageMap);

  useEffect(() => {
    setCurrentLanguage(i18n.language);
  }, [i18n.language]);

  const changeLanguage = async (languageCode) => {
    if (!supportedLanguages.includes(languageCode)) {
      console.warn(`Language ${languageCode} is not supported`);
      return;
    }

    setIsTranslating(true);
    try {
      await i18n.changeLanguage(languageCode);
      setCurrentLanguage(languageCode);
      
      // Store language preference in localStorage
      localStorage.setItem('preferred-language', languageCode);
    } catch (error) {
      console.error('Failed to change language:', error);
    } finally {
      setIsTranslating(false);
    }
  };

  const getCurrentLanguageInfo = () => {
    return languageMap[currentLanguage] || languageMap.en;
  };

  const value = {
    currentLanguage,
    changeLanguage,
    isTranslating,
    supportedLanguages,
    languageMap,
    getCurrentLanguageInfo,
  };

  return (
    <TranslationContext.Provider value={value}>
      {children}
    </TranslationContext.Provider>
  );
};
