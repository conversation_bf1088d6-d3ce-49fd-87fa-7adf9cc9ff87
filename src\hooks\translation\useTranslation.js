import { useTranslation as useI18nTranslation } from 'react-i18next';
import { useDynamicTranslation } from './useDynamicTranslation';
import { useTranslationContext } from '../../contexts/TranslationContext';

export const useTranslation = () => {
  const { t, i18n } = useI18nTranslation();
  const { currentLanguage, changeLanguage, isTranslating: isChangingLanguage } = useTranslationContext();
  const { 
    translateText, 
    translateTexts, 
    translateObject, 
    translateObjectArray, 
    isTranslating: isDynamicTranslating 
  } = useDynamicTranslation();

  // Static translation function (for UI elements)
  const translate = (key, options = {}) => {
    return t(key, options);
  };

  // Dynamic translation function (for database content)
  const translateDynamic = async (text, targetLanguage = currentLanguage) => {
    return await translateText(text, targetLanguage);
  };

  // Translate product data
  const translateProduct = async (product) => {
    if (!product || currentLanguage === 'en') return product;
    
    return await translateObject(product, ['name', 'description', 'shortDescription'], currentLanguage);
  };

  // Translate blog post data
  const translateBlogPost = async (post) => {
    if (!post || currentLanguage === 'en') return post;
    
    return await translateObject(post, ['title', 'content', 'excerpt', 'summary'], currentLanguage);
  };

  // Translate FAQ data
  const translateFAQ = async (faq) => {
    if (!faq || currentLanguage === 'en') return faq;
    
    return await translateObject(faq, ['title', 'content', 'question', 'answer'], currentLanguage);
  };

  // Translate category data
  const translateCategory = async (category) => {
    if (!category || currentLanguage === 'en') return category;
    
    return await translateObject(category, ['name', 'description'], currentLanguage);
  };

  // Translate array of products
  const translateProducts = async (products) => {
    if (!products || !Array.isArray(products) || currentLanguage === 'en') return products;
    
    return await translateObjectArray(products, ['name', 'description', 'shortDescription'], currentLanguage);
  };

  // Translate array of blog posts
  const translateBlogPosts = async (posts) => {
    if (!posts || !Array.isArray(posts) || currentLanguage === 'en') return posts;
    
    return await translateObjectArray(posts, ['title', 'content', 'excerpt', 'summary'], currentLanguage);
  };

  // Translate array of FAQs
  const translateFAQs = async (faqs) => {
    if (!faqs || !Array.isArray(faqs) || currentLanguage === 'en') return faqs;
    
    return await translateObjectArray(faqs, ['title', 'content', 'question', 'answer'], currentLanguage);
  };

  // Translate array of categories
  const translateCategories = async (categories) => {
    if (!categories || !Array.isArray(categories) || currentLanguage === 'en') return categories;
    
    return await translateObjectArray(categories, ['name', 'description'], currentLanguage);
  };

  return {
    // Static translations
    t: translate,
    
    // Dynamic translations
    translateDynamic,
    translateText,
    translateTexts,
    translateObject,
    translateObjectArray,
    
    // Specific content type translations
    translateProduct,
    translateBlogPost,
    translateFAQ,
    translateCategory,
    translateProducts,
    translateBlogPosts,
    translateFAQs,
    translateCategories,
    
    // Language management
    currentLanguage,
    changeLanguage,
    
    // Loading states
    isTranslating: isChangingLanguage || isDynamicTranslating,
    isChangingLanguage,
    isDynamicTranslating,
    
    // i18n instance for advanced usage
    i18n,
  };
};
