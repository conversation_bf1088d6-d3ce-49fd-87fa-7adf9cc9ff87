import {Link, useParams} from "react-router-dom";
import MenuSideBar from "./componets/MenuSideBar";
import MainHeader from "./componets/MainHeader";
import RightSideBar from "./componets/RightSideBar";
import {useTabs} from "../services/CustomTabs";

// import Swiper core and required modules
// Import Swiper styles
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import "swiper/css/scrollbar";
import MainFooter from "./componets/MainFooter";
import HeroProduct from "./componets/product-details/HeroProduct.jsx";
import {useOfferDetails} from "../hooks/offers/useOfferDetails.js";
import SellerItem from "./componets/product-details/SellerItem.jsx";
import SimilarOffers from "./componets/product-details/SimilarOffers.jsx";
import Spinner from "../components/common/Spinner.jsx";
import ProductReviews from "../components/product-details/productReviews.jsx";
import ActivationGuide from "../components/product-details/activationGuide.jsx";
import {useRef} from "react";
import { useTranslation } from "../hooks/translation/useTranslation";
import { useTranslatedData } from "../hooks/translation/useTranslatedData";


export default function ProductDetails() {
  const { t } = useTranslation();
  const {id} = useParams();
  const {offer} = useOfferDetails({_id: id});

  // Translate the offer data
  const { data: translatedOffer, isTranslating } = useTranslatedData(
    offer?.data,
    ['name', 'description', 'shortDescription'],
    [offer]
  );

  return (
      <>

      <div className="d-flex main_house_con">
        <MenuSideBar makeShort={true} activeLink={offer?.data.category} />

        <div
          className="col housing_con d-flex flex-column"
          style={{
            backgroundImage: " url('./assets/images/psn_bg.png')",
          }}>
          <MainHeader activeLink={offer?.data.category} />

          <div className="housing d-flex gap-1 position-relative">
            <div id="scrollable-section" className="col main_section">
              <div className="d-flex align-items-center gap-2 mb-3">
                <Link to={"/"}>
                  <span className="crumb_icon">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="1em"
                      height="1em"
                      viewBox="0 0 24 24">
                      <path
                        fill="currentColor"
                        d="M13.45 2.533a2.25 2.25 0 0 0-2.9 0L3.8 8.228a2.25 2.25 0 0 0-.8 1.72v9.305c0 .966.784 1.75 1.75 1.75h3a1.75 1.75 0 0 0 1.75-1.75V15.25c0-.68.542-1.232 1.217-1.25h2.566a1.25 1.25 0 0 1 1.217 1.25v4.003c0 .966.784 1.75 1.75 1.75h3a1.75 1.75 0 0 0 1.75-1.75V9.947a2.25 2.25 0 0 0-.8-1.72z"
                      />
                    </svg>
                  </span>
                </Link>
                <Link>
                  <p className="crumb_link">/ PC Gaming</p>
                </Link>
                <Link>
                  <p className="crumb_link">/ Stream</p>
                </Link>
              </div>

              <RenderDetails offer={offer}/>

              <div className="col d-lg-none">
                <MainFooter />
              </div>
            </div>

            <RightSideBar makeOpen={false} />
          </div>
        </div>
      </div>
    </>
  );
}


const RenderDetails = ({offer}) => {

  const Tab1 = useTabs(1);
  const Tab2 = useTabs(1);

  const reviewsRef = useRef(null);
  const offersRef = useRef(null);

  const scrollToReviews = () => {
    reviewsRef.current?.scrollIntoView({ behavior: 'smooth' });
  }

  const scrollToOffers = () => {
    offersRef.current?.scrollIntoView({ behavior: 'smooth' });
  }

  if(!offer) return <Spinner />

  return (
      <div className="main_cont">
        <HeroProduct
            seller={translatedOffer?.seller || offer.data.seller}
            offer={translatedOffer || offer.data}
            isTranslating={isTranslating}
            onClick={() => {
              Tab1.ChangeTab(2);
              scrollToReviews();
            }}
            fromClick={() => scrollToOffers()}
        />
        <div className="col mb-5">
          <div className="col mb-3">
            <h4 className="details_title" ref={offersRef}>
              {t('product.similarItems')}
            </h4>
            <p className="details_tit_sm">
              {t('product.basedOnCustomers')}
            </p>
          </div>

            <SimilarOffers category={offer.data.category} subCategory={offer.data.subcategory}/>
        </div>

        {offer.data.category !== "software" && offer.lowestCustomerPays && <div className="col mb-4">
          <div className="col d-flex flex-wrap gap-3 align-items-center mb-3">
            <div className="col-12 col-md">
              <h4 className="details_title">
                Offers from other sellers
              </h4>
              <p className="details_tit">
                Lowest price <span>${offer.lowestCustomerPays}</span>
              </p>
            </div>
            <select name="" className="col col-md-auto details_fil_sel">
              <option value="">Sort by: Best price</option>
            </select>
          </div>

          {/* Details Offers */}
          <div className="col gap-3 mb-5">
            {offer.relatedOffers.map((item, index) => <SellerItem key={index} {...item}/>)}
          </div>
        </div>}

        <div className="col" ref={reviewsRef}>
          <div
              className="col d-flex gap-2 justify-content-start justify-content-md-center align-items-center overflow-auto hide_scroll mb-4">
            <p
                className={
                    "details_tab_link " +
                    (Tab1.activeTab == 1 ? "active" : "")
                }
                onClick={() => Tab1.ChangeTab(1)}>
              Description
            </p>
            <p
                className={
                    "details_tab_link d-flex gap-1 align-items-center " +
                    (Tab1.activeTab == 2 ? "active" : "")
                }
                onClick={() => Tab1.ChangeTab(2)}>
              Reviews ({offer.data.ratings.length})
              <span className="d-flex align-items-center">
                        <svg
                            className="ms-1"
                            xmlns="http://www.w3.org/2000/svg"
                            width="1em"
                            height="1em"
                            viewBox="0 0 24 24">
                          <path
                              fill="currentColor"
                              d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2L9.19 8.63L2 9.24l5.46 4.73L5.82 21z"
                          />
                        </svg>
                {offer.data.averageRating}
                      </span>
            </p>
            <p
                className={
                    "details_tab_link " +
                    (Tab1.activeTab == 3 ? "active" : "")
                }
                onClick={() => Tab1.ChangeTab(3)}>
              System Requirements
            </p>
            <p
                className={
                    "details_tab_link " +
                    (Tab1.activeTab == 4 ? "active" : "")
                }
                onClick={() => Tab1.ChangeTab(4)}>
              Key Activation
            </p>
          </div>

          {/* Description */}
          {Tab1.activeTab == 1 && (
              <div className="col details_tab_cont"
                   dangerouslySetInnerHTML={{__html: offer.data.template.details.description}}/>
          )}

          {/* Reviews */}
          {Tab1.activeTab === 2 && (
              <ProductReviews ratings={offer.data.ratings} averageRating={offer.data.averageRating}/>
          )}


          {/* Requirement */}
          {Tab1.activeTab == 3 && (
              <div className="col details_tab_cont">
                <div
                    className="col details_req_tab_con d-flex gap-2 justify-content-start justify-content-md-center mb-3">
                  <p
                      className={
                          "details_tab_link small " +
                          (Tab2.activeTab == 1 ? "active" : "")
                      }
                      onClick={() => Tab2.ChangeTab(1)}>
                    Windows
                  </p>
                  <p
                      className={
                          "details_tab_link small " +
                          (Tab2.activeTab == 2 ? "active" : "")
                      }
                      onClick={() => Tab2.ChangeTab(2)}>
                    Mac
                  </p>
                </div>

                {/* Windows */}
                {Tab2.activeTab == 1 && (
                    <div className="col d-flex flex-wrap gap-3">
                      <div className="col-12 col-md">
                        <h4 className="details_req_header">
                          Minimal requirements
                        </h4>

                        <div className="col mb-3">
                          <p className="details_req_head">
                            Minimal requirements
                          </p>
                          <p className="details_req_text">
                            AMD Ryzen 3 1200 ／ Intel Core i5-7500
                          </p>
                        </div>
                        <div className="col mb-3">
                          <p className="details_req_head">GRAPHICS</p>
                          <p className="details_req_text">
                            AMD Radeon RX 560 with 4GB VRAM ／ NVIDIA
                            GeForce GTX 1050 Ti with 4GB VRAM
                          </p>
                        </div>
                        <div className="col mb-3">
                          <p className="details_req_head">Memory</p>
                          <p className="details_req_text">
                            AMD Radeon RX 560 with 4GB VRAM ／ NVIDIA
                            GeForce GTX 1050 Ti with 4GB VRAM
                          </p>
                        </div>
                        <div className="col mb-3">
                          <p className="details_req_head">System</p>
                          <p className="details_req_text">
                            AMD Radeon RX 560 with 4GB VRAM ／ NVIDIA
                            GeForce GTX 1050 Ti with 4GB VRAM
                          </p>
                        </div>
                        <div className="col mb-3">
                          <p className="details_req_head">OTHER</p>
                          <p className="details_req_text">
                            Estimated performance (when set to Prioritize
                            Performance): 1080p/60fps. ・Framerate might
                            drop in graphics-intensive scenes. ・AMD Radeon
                            RX 6700 XT or NVIDIA GeForce RTX 2060 required
                            to support ray tracing. System requirements
                            subject to change during game development.
                          </p>
                        </div>
                      </div>
                      <div className="col-12 col-md">
                        <h4 className="details_req_header">
                          Recommended requirements
                        </h4>

                        <div className="col mb-3">
                          <p className="details_req_head">
                            Minimal requirements
                          </p>
                          <p className="details_req_text">
                            AMD Ryzen 3 1200 ／ Intel Core i5-7500
                          </p>
                        </div>
                        <div className="col mb-3">
                          <p className="details_req_head">GRAPHICS</p>
                          <p className="details_req_text">
                            AMD Radeon RX 5700 ／ NVIDIA GeForce GTX 1070
                          </p>
                        </div>
                        <div className="col mb-3">
                          <p className="details_req_head">Memory</p>
                          <p className="details_req_text">16 GB RAM</p>
                        </div>
                        <div className="col mb-3">
                          <p className="details_req_head">System</p>
                          <p className="details_req_text">
                            Windows 10 (64 bit)
                          </p>
                        </div>
                      </div>
                    </div>
                )}
                {/* Mac */}
                {Tab2.activeTab == 2 && (
                    <div className="col d-flex flex-wrap gap-3">
                      <div className="col-12 col-md">
                        <h4 className="details_req_header">
                          Minimal requirements
                        </h4>

                        <div className="col mb-3">
                          <p className="details_req_head">
                            Minimal requirements
                          </p>
                          <p className="details_req_text">
                            AMD Ryzen 3 1200 ／ Intel Core i5-7500
                          </p>
                        </div>
                        <div className="col mb-3">
                          <p className="details_req_head">GRAPHICS</p>
                          <p className="details_req_text">
                            AMD Radeon RX 560 with 4GB VRAM ／ NVIDIA
                            GeForce GTX 1050 Ti with 4GB VRAM
                          </p>
                        </div>
                        <div className="col mb-3">
                          <p className="details_req_head">Memory</p>
                          <p className="details_req_text">
                            AMD Radeon RX 560 with 4GB VRAM ／ NVIDIA
                            GeForce GTX 1050 Ti with 4GB VRAM
                          </p>
                        </div>
                        <div className="col mb-3">
                          <p className="details_req_head">System</p>
                          <p className="details_req_text">
                            AMD Radeon RX 560 with 4GB VRAM ／ NVIDIA
                            GeForce GTX 1050 Ti with 4GB VRAM
                          </p>
                        </div>
                        <div className="col mb-3">
                          <p className="details_req_head">OTHER</p>
                          <p className="details_req_text">
                            Estimated performance (when set to Prioritize
                            Performance): 1080p/60fps. ・Framerate might
                            drop in graphics-intensive scenes. ・AMD Radeon
                            RX 6700 XT or NVIDIA GeForce RTX 2060 required
                            to support ray tracing. System requirements
                            subject to change during game development.
                          </p>
                        </div>
                      </div>
                      <div className="col-12 col-md">
                        <h4 className="details_req_header">
                          Recommended requirements
                        </h4>

                        <div className="col mb-3">
                          <p className="details_req_head">
                            Minimal requirements
                          </p>
                          <p className="details_req_text">
                            AMD Ryzen 3 1200 ／ Intel Core i5-7500
                          </p>
                        </div>
                        <div className="col mb-3">
                          <p className="details_req_head">GRAPHICS</p>
                          <p className="details_req_text">
                            AMD Radeon RX 5700 ／ NVIDIA GeForce GTX 1070
                          </p>
                        </div>
                        <div className="col mb-3">
                          <p className="details_req_head">Memory</p>
                          <p className="details_req_text">16 GB RAM</p>
                        </div>
                        <div className="col mb-3">
                          <p className="details_req_head">System</p>
                          <p className="details_req_text">
                            Windows 10 (64 bit)
                          </p>
                        </div>
                      </div>
                    </div>
                )}
              </div>
          )}

          {/* Key Activation */}
          {Tab1.activeTab == 4 && <ActivationGuide/>}
        </div>
      </div>
  )
}