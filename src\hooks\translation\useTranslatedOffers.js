import { useState, useEffect } from 'react';
import { useTranslation } from './useTranslation';

/**
 * Hook specifically for translating offers/products data
 * This handles the complex structure of offers from your API
 */
export const useTranslatedOffers = (offers) => {
  const { currentLanguage, translateObjectArray } = useTranslation();
  const [translatedOffers, setTranslatedOffers] = useState(offers);
  const [isTranslating, setIsTranslating] = useState(false);

  useEffect(() => {
    const translateOffers = async () => {
      if (!offers || !offers.data || !Array.isArray(offers.data) || currentLanguage === 'en') {
        setTranslatedOffers(offers);
        return;
      }

      setIsTranslating(true);
      try {
        // Translate the offers data array
        const translated = await translateObjectArray(
          offers.data, 
          ['name', 'description', 'shortDescription'], 
          currentLanguage
        );
        
        // Maintain the original structure but with translated data
        setTranslatedOffers({
          ...offers,
          data: translated
        });
      } catch (error) {
        console.error('Failed to translate offers:', error);
        setTranslatedOffers(offers); // Fallback to original
      } finally {
        setIsTranslating(false);
      }
    };

    translateOffers();
  }, [offers, currentLanguage, translateObjectArray]);

  return {
    offers: translatedOffers,
    isTranslating,
    originalOffers: offers
  };
};
