import { useState, useEffect } from 'react';
import { useTranslation } from './useTranslation';

/**
 * Custom hook to automatically translate data from API responses
 * This hook wraps your existing data hooks and provides translated versions
 */
export const useTranslatedData = (data, fieldsToTranslate = [], dependencies = []) => {
  const { currentLanguage, translateObjectArray, translateObject } = useTranslation();
  const [translatedData, setTranslatedData] = useState(data);
  const [isTranslating, setIsTranslating] = useState(false);

  useEffect(() => {
    const translateData = async () => {
      if (!data || currentLanguage === 'en') {
        setTranslatedData(data);
        return;
      }

      setIsTranslating(true);
      try {
        let translated;
        
        if (Array.isArray(data)) {
          translated = await translateObjectArray(data, fieldsToTranslate, currentLanguage);
        } else if (typeof data === 'object' && data !== null) {
          translated = await translateObject(data, fieldsToTranslate, currentLanguage);
        } else {
          translated = data;
        }
        
        setTranslatedData(translated);
      } catch (error) {
        console.error('Translation failed:', error);
        setTranslatedData(data); // Fallback to original data
      } finally {
        setIsTranslating(false);
      }
    };

    translateData();
  }, [data, currentLanguage, fieldsToTranslate, translateObjectArray, translateObject, ...dependencies]);

  return {
    data: translatedData,
    isTranslating,
    originalData: data
  };
};

/**
 * Hook specifically for FAQ data
 */
export const useTranslatedFAQ = (faqData) => {
  return useTranslatedData(faqData, ['title', 'content', 'question', 'answer'], [faqData]);
};

/**
 * Hook specifically for blog data
 */
export const useTranslatedBlogs = (blogData) => {
  return useTranslatedData(blogData, ['title', 'content', 'excerpt', 'summary'], [blogData]);
};

/**
 * Hook specifically for category data
 */
export const useTranslatedCategories = (categoryData) => {
  return useTranslatedData(categoryData, ['name', 'description'], [categoryData]);
};

/**
 * Hook specifically for product/offer data
 */
export const useTranslatedProducts = (productData) => {
  return useTranslatedData(productData, ['name', 'description', 'shortDescription', 'title'], [productData]);
};

/**
 * Hook for help center data
 */
export const useTranslatedHelp = (helpData) => {
  return useTranslatedData(helpData, ['title', 'content', 'description'], [helpData]);
};
