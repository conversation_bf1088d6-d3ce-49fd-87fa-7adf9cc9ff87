// LibreTranslate API Service
// This service handles all communication with LibreTranslate API for dynamic content translation

class TranslationService {
  constructor() {
    // You can use the free public instance or self-host LibreTranslate
    this.apiUrl = 'https://libretranslate.de/translate';
    
    // Alternative self-hosted option (if you want to set up your own instance):
    // this.apiUrl = 'http://localhost:5000/translate';
    
    this.cache = new Map();
    this.requestQueue = [];
    this.isProcessing = false;
    
    // Rate limiting to avoid overwhelming the free API
    this.rateLimitDelay = 1000; // 1 second between requests
    this.maxRetries = 3;
  }

  // Language code mapping for LibreTranslate
  getLanguageCode(lang) {
    const mapping = {
      en: 'en',
      es: 'es',
      fr: 'fr', 
      lv: 'lv',
      pl: 'pl'
    };
    return mapping[lang] || lang;
  }

  // Generate cache key
  getCacheKey(text, source, target) {
    return `${text}_${source}_${target}`;
  }

  // Check if translation is cached
  getCachedTranslation(text, source, target) {
    const key = this.getCacheKey(text, source, target);
    return this.cache.get(key);
  }

  // Cache translation result
  cacheTranslation(text, source, target, translation) {
    const key = this.getCacheKey(text, source, target);
    this.cache.set(key, translation);
    
    // Limit cache size to prevent memory issues
    if (this.cache.size > 1000) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
  }

  // Process translation queue with rate limiting
  async processQueue() {
    if (this.isProcessing || this.requestQueue.length === 0) {
      return;
    }

    this.isProcessing = true;

    while (this.requestQueue.length > 0) {
      const request = this.requestQueue.shift();
      try {
        const result = await this.makeTranslationRequest(request.text, request.source, request.target);
        request.resolve(result);
      } catch (error) {
        request.reject(error);
      }
      
      // Rate limiting delay
      if (this.requestQueue.length > 0) {
        await new Promise(resolve => setTimeout(resolve, this.rateLimitDelay));
      }
    }

    this.isProcessing = false;
  }

  // Make actual API request to LibreTranslate
  async makeTranslationRequest(text, source, target, retryCount = 0) {
    try {
      const response = await fetch(this.apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          q: text,
          source: this.getLanguageCode(source),
          target: this.getLanguageCode(target),
          format: 'text'
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      if (!data.translatedText) {
        throw new Error('No translation returned from API');
      }

      return data.translatedText;
    } catch (error) {
      if (retryCount < this.maxRetries) {
        console.warn(`Translation retry ${retryCount + 1}/${this.maxRetries} for: ${text.substring(0, 50)}...`);
        await new Promise(resolve => setTimeout(resolve, 2000 * (retryCount + 1))); // Exponential backoff
        return this.makeTranslationRequest(text, source, target, retryCount + 1);
      }
      throw error;
    }
  }

  // Main translation method
  async translate(text, targetLanguage = 'en', sourceLanguage = 'en') {
    // Return original text if same language or target is English
    if (sourceLanguage === targetLanguage || targetLanguage === 'en') {
      return text;
    }

    // Validate input
    if (!text || typeof text !== 'string' || text.trim().length === 0) {
      return text;
    }

    // Skip very short text
    if (text.trim().length < 3) {
      return text;
    }

    // Check cache first
    const cached = this.getCachedTranslation(text, sourceLanguage, targetLanguage);
    if (cached) {
      return cached;
    }

    // Add to queue and return promise
    return new Promise((resolve, reject) => {
      this.requestQueue.push({
        text,
        source: sourceLanguage,
        target: targetLanguage,
        resolve: (result) => {
          this.cacheTranslation(text, sourceLanguage, targetLanguage, result);
          resolve(result);
        },
        reject: (error) => {
          console.error('Translation failed:', error);
          // Return original text on error
          resolve(text);
        }
      });

      // Start processing queue
      this.processQueue();
    });
  }

  // Translate multiple texts
  async translateBatch(texts, targetLanguage = 'en', sourceLanguage = 'en') {
    const promises = texts.map(text => this.translate(text, targetLanguage, sourceLanguage));
    return Promise.all(promises);
  }

  // Translate object properties
  async translateObject(obj, fieldsToTranslate = [], targetLanguage = 'en', sourceLanguage = 'en') {
    if (!obj || typeof obj !== 'object') {
      return obj;
    }

    const translatedObj = { ...obj };
    const translationPromises = [];

    for (const field of fieldsToTranslate) {
      if (obj[field] && typeof obj[field] === 'string') {
        translationPromises.push(
          this.translate(obj[field], targetLanguage, sourceLanguage)
            .then(translated => {
              translatedObj[field] = translated;
            })
        );
      }
    }

    await Promise.all(translationPromises);
    return translatedObj;
  }

  // Clear cache
  clearCache() {
    this.cache.clear();
  }

  // Get cache stats
  getCacheStats() {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys()).slice(0, 10) // First 10 keys for debugging
    };
  }
}

// Create singleton instance
const translationService = new TranslationService();

export default translationService;
