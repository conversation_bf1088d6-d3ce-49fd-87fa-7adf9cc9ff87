import React, { useState, useEffect } from 'react';
import { useTranslation } from '../../hooks/translation/useTranslation';

const TranslationDemo = () => {
  const { t, translateDynamic, currentLanguage, changeLanguage, isTranslating } = useTranslation();
  const [dynamicText, setDynamicText] = useState('');
  const [translatedText, setTranslatedText] = useState('');

  // Sample dynamic content (like what would come from your database)
  const sampleContent = {
    productName: "Gaming Headset Pro",
    productDescription: "High-quality wireless gaming headset with noise cancellation and crystal clear audio. Perfect for competitive gaming and long gaming sessions.",
    blogTitle: "Top 10 Gaming Tips for Beginners",
    blogContent: "Gaming can be challenging for newcomers. Here are some essential tips to help you improve your skills and enjoy your gaming experience more."
  };

  // Translate dynamic content when language changes
  useEffect(() => {
    const translateContent = async () => {
      if (currentLanguage === 'en') {
        setTranslatedText(sampleContent.productDescription);
        return;
      }

      try {
        const translated = await translateDynamic(sampleContent.productDescription);
        setTranslatedText(translated);
      } catch (error) {
        console.error('Translation failed:', error);
        setTranslatedText(sampleContent.productDescription);
      }
    };

    translateContent();
  }, [currentLanguage, translateDynamic]);

  const handleDynamicTranslation = async () => {
    if (!dynamicText.trim()) return;
    
    try {
      const translated = await translateDynamic(dynamicText);
      setTranslatedText(translated);
    } catch (error) {
      console.error('Translation failed:', error);
    }
  };

  return (
    <div className="translation-demo p-4 border rounded">
      <h3>{t('common.translation')} Demo</h3>
      
      {/* Language Selector */}
      <div className="mb-3">
        <label className="form-label">{t('common.language')}:</label>
        <select 
          className="form-select" 
          value={currentLanguage} 
          onChange={(e) => changeLanguage(e.target.value)}
          disabled={isTranslating}
        >
          <option value="en">English</option>
          <option value="es">Español</option>
          <option value="fr">Français</option>
          <option value="lv">Latviešu</option>
          <option value="pl">Polski</option>
        </select>
      </div>

      {/* Static Translation Examples */}
      <div className="mb-4">
        <h4>{t('common.static')} Translations</h4>
        <ul>
          <li><strong>{t('navigation.home')}</strong> (navigation.home)</li>
          <li><strong>{t('navigation.cart')}</strong> (navigation.cart)</li>
          <li><strong>{t('shop.addToCart')}</strong> (shop.addToCart)</li>
          <li><strong>{t('common.loading')}</strong> (common.loading)</li>
          <li><strong>{t('forms.email')}</strong> (forms.email)</li>
        </ul>
      </div>

      {/* Dynamic Translation Example */}
      <div className="mb-4">
        <h4>Dynamic Content Translation</h4>
        <div className="mb-3">
          <label className="form-label">Original Text (English):</label>
          <div className="p-2 bg-light border rounded">
            {sampleContent.productDescription}
          </div>
        </div>
        
        <div className="mb-3">
          <label className="form-label">Translated Text ({currentLanguage.toUpperCase()}):</label>
          <div className="p-2 bg-light border rounded">
            {isTranslating ? (
              <span className="text-muted">Translating...</span>
            ) : (
              translatedText || sampleContent.productDescription
            )}
          </div>
        </div>
      </div>

      {/* Custom Text Translation */}
      <div className="mb-4">
        <h4>Try Your Own Text</h4>
        <div className="mb-3">
          <label className="form-label">Enter text to translate:</label>
          <textarea
            className="form-control"
            rows="3"
            value={dynamicText}
            onChange={(e) => setDynamicText(e.target.value)}
            placeholder="Enter English text here..."
          />
        </div>
        <button 
          className="btn btn-primary"
          onClick={handleDynamicTranslation}
          disabled={!dynamicText.trim() || isTranslating}
        >
          {isTranslating ? 'Translating...' : `Translate to ${currentLanguage.toUpperCase()}`}
        </button>
      </div>

      {/* Translation Status */}
      <div className="alert alert-info">
        <strong>Current Language:</strong> {currentLanguage.toUpperCase()}<br/>
        <strong>Translation Status:</strong> {isTranslating ? 'Translating...' : 'Ready'}<br/>
        <strong>Note:</strong> Dynamic translations use LibreTranslate API. Static UI translations use i18next.
      </div>
    </div>
  );
};

export default TranslationDemo;
