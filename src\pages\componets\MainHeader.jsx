/* eslint-disable react/prop-types */
import {useContext, useEffect, useState} from "react";
import {Link, useLocation} from "react-router-dom";
import {NavContext} from "../../store/NavContext";
import MenuSideBarContent from "./MenuSideBarContent";
import AccountSideBarContent from "./account/AccountSideBarContent";
import CustomDropdown from "./utility/CustomDropdown";
import {useProfile} from "../../hooks/auth/useProfile.js";
import {useCart} from "../../hooks/cart/useCart.js";
import {useDebounce} from "../../hooks/common/useDebounce.js";
import {useSellerOffers} from "../../hooks/offers/useSellerOffers.js";
import {calculateSavingsPercentage, generateQuery} from "../../vbrae-utils/lib/misc.js";
import {getCheckIcon, getIcon} from "../../vbrae-utils/lib/getIcons.jsx";
import Spinner from "../../components/common/Spinner.jsx";
import Notifications from "./header/notifications/Notifications.jsx";
import MobileNotifications from "./header/notifications/MobileNotifications.jsx";
import MobileGuest from "./header/MobileGuest.jsx";
import MobileLoggedIn from "./header/MobileLoggedIn.jsx";
import WebLoggedIn from "./header/WebLoggedIn.jsx";
import WebGuest from "./header/WebGuest.jsx";
import TabNotifications from "./header/notifications/TabNotifications.jsx";
import MobileCart from "./header/cart/MobileCart.jsx";
import TabCart from "./header/cart/TabCart.jsx";
import { useTranslation } from "../../hooks/translation/useTranslation";

function SearchResult({ isOpen, results=[], total, searchTerm }) {
  const { t } = useTranslation();

  return (
    <>
      {isOpen && (
        <div className="col-12 search_result_con d-flex flex-column position-absolute">
          {results.map(item=> {
            const [p1, p2] = item.customerPays.toString().split(".");
            return (
                <div className="search_result_cont d-flex gap-3 align-items-start mb-3" key={item._id}>
                  <img
                      src={item.template.coverImage}
                      alt=""
                      className="col-auto search_result_img"
                  />
                  <div className="col-9">
                    <Link to={`/details/${item._id}`}>
                      <p className="cart_item_name truncate mb-1">
                        {item.name} / {item.category} {item.instantDelivery ? '[Digital Code]' : ''} {item.region}
                      </p>
                    </Link>
                    <div className="d-flex gap-2 align-items-center">
                      <p className="cart_item_price text-white">
                        ${p1}{p2 && <span>.{p2}</span>}
                      </p>
                      <span className="cart_item_dis">{calculateSavingsPercentage(item.template.price,item.customerPays)}%</span>
                    </div>
                    <div className="d-flex align-items-end gap-3">
                      <div className="d-flex flex-wrap">
                  <span className="game_badge type2 me-1 mb-1">
                    <svg
                        className="me-1"
                        xmlns="http://www.w3.org/2000/svg"
                        width="1em"
                        height="1em"
                        viewBox="0 0 256 256">
                      <path
                          fill="currentColor"
                          d="M143.38 17.85a8 8 0 0 0-12.63 3.41l-22 60.41l-24.16-23.41a8 8 0 0 0-11.93.89C51 87.53 40 116.08 40 144a88 88 0 0 0 176 0c0-59.45-50.79-108-72.62-126.15m40.51 135.49a57.6 57.6 0 0 1-46.56 46.55a7.7 7.7 0 0 1-1.33.11a8 8 0 0 1-1.32-15.89c16.57-2.79 30.63-16.85 33.44-33.45a8 8 0 0 1 15.78 2.68Z"
                      />
                    </svg>
                    Hot
                  </span>
                        {item.instantDelivery &&
                            <span className="game_badge type3 me-1 mb-1">
                        <svg
                            className="me-1"
                            xmlns="http://www.w3.org/2000/svg"
                            width="1em"
                            height="1em"
                            viewBox="0 0 16 16">
                          <path
                              fill="currentColor"
                              d="M11.251.068a.5.5 0 0 1 .227.58L9.677 6.5H13a.5.5 0 0 1 .364.843l-8 8.5a.5.5 0 0 1-.842-.49L6.323 9.5H3a.5.5 0 0 1-.364-.843l8-8.5a.5.5 0 0 1 .615-.09z"
                          />
                        </svg>
                        Instant
                      </span>}
                      </div>
                      <div className="d-flex justify-content-end">
                        {item.region === 'GLOBAL' && <span className="game_img_icon me-2">
                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="1em"
                        height="1em"
                        viewBox="0 0 24 24">
                      <path
                          fill="currentColor"
                          d="M2.05 13h5.477a17.9 17.9 0 0 0 2.925 8.88A10.005 10.005 0 0 1 2.049 13m0-2a10.005 10.005 0 0 1 8.402-8.881a17.9 17.9 0 0 0-2.925 8.88zm19.9 0h-5.477a17.9 17.9 0 0 0-2.925-8.881a10.005 10.005 0 0 1 8.403 8.88m0 2a10.005 10.005 0 0 1-8.402 8.88A17.9 17.9 0 0 0 16.473 13zM9.53 13h4.94A15.9 15.9 0 0 1 12 20.592A15.9 15.9 0 0 1 9.53 13m0-2A15.9 15.9 0 0 1 12 3.408A15.9 15.9 0 0 1 14.47 11z"
                      />
                    </svg>
                  </span>}
                        {item.instantDelivery && <span className="game_img_icon me-2">
                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="1em"
                        height="1em"
                        viewBox="0 0 24 24">
                      <g fill="none">
                        <path
                            d="m12.593 23.258l-.011.002l-.071.035l-.02.004l-.014-.004l-.071-.035q-.016-.005-.024.005l-.004.01l-.017.428l.005.02l.01.013l.104.074l.015.004l.012-.004l.104-.074l.012-.016l.004-.017l-.017-.427q-.004-.016-.017-.018m.265-.113l-.013.002l-.185.093l-.01.01l-.003.011l.018.43l.005.012l.008.007l.201.093q.019.005.029-.008l.004-.014l-.034-.614q-.005-.018-.02-.022m-.715.002a.02.02 0 0 0-.027.006l-.006.014l-.034.614q.001.018.017.024l.015-.002l.201-.093l.01-.008l.004-.011l.017-.43l-.003-.012l-.01-.01z"/>
                        <path
                            fill="currentColor"
                            d="M10.438 4.368a6.5 6.5 0 1 1 2.252 10.66l-.269-.11l-.02-.004h-.61v1.578a1.25 1.25 0 0 1-1.122 1.244l-.128.006H8.963v1.578a1.25 1.25 0 0 1-1.122 1.244l-.128.006H3.73a1.01 1.01 0 0 1-1.004-.9l-.006-.11v-2.61a1.5 1.5 0 0 1 .34-.951l.1-.11l5.5-5.501l.01-.037a.3.3 0 0 0-.004-.081a6.5 6.5 0 0 1 1.772-5.902m4.242 2.828a1.5 1.5 0 1 0 2.122 2.121a1.5 1.5 0 0 0-2.122-2.12Z"
                        />
                      </g>
                    </svg>
                  </span>}
                        <span className="game_img_icon me-2">
                          {getIcon(item.category)}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
            )
          })}

          <div className="col-auto d-flex gap-3 justify-content-center align-items-center mt-auto">
            <p className="search_result_text mb-0">{total} {t('search.searchResults')}</p>
            {total > 0 && <Link to={`/search?keyword=${searchTerm}`} className="search_result_text link">
              {t('buttons.viewAll')}
              <span>
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="1.2em"
                    height="1.2em"
                    viewBox="0 0 24 24">
                  <path
                      fill="none"
                      stroke="currentColor"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="m9 5l7 7l-7 7"></path>
                </svg>
              </span>
            </Link>}
          </div>
        </div>
      )}
    </>
  );
}

function MobileSearch({isOpen, onClose}) {
  const { t } = useTranslation();
  const [searchInput, setSearchInput] = useState("");
  const searchTerm = useDebounce(searchInput, 500);
  const {offers, offersLoading} = useSellerOffers({
    query: generateQuery({
      staticString: `search=${searchTerm}`,
      limit: 5,
    })
  });
  const [isResultOpen, setResultOpen] = useState(false);

  function handleSearchInput(e) {
    setSearchInput(e.target.value);

    // Display a message when typing starts
    if (e.target.value) {
      setResultOpen(true);
    } else {
      setResultOpen(false);
    }
  }

  if (isOpen) {
    return (
        <div className="mob_search_con d-flex gap-3 align-items-center position-absolute">
          <div className="mob_search_cont d-flex gap-3">
          <span className="mob_header_icon">
            <svg
                xmlns="http://www.w3.org/2000/svg"
                width="1em"
                height="1em"
                viewBox="0 0 24 24">
              <path
                  fill="currentColor"
                  d="m18.031 16.617l4.283 4.282l-1.415 1.415l-4.282-4.283A8.96 8.96 0 0 1 11 20c-4.968 0-9-4.032-9-9s4.032-9 9-9s9 4.032 9 9a8.96 8.96 0 0 1-1.969 5.617m-2.006-.742A6.98 6.98 0 0 0 18 11c0-3.867-3.133-7-7-7s-7 3.133-7 7s3.133 7 7 7a6.98 6.98 0 0 0 4.875-1.975z"></path>
            </svg>
          </span>
            <input
                type="text"
                className="box"
                placeholder={t('search.placeholder')}
                value={searchInput}
                onChange={handleSearchInput}
            />
          </div>
          <span className="mob_header_icon dark" onClick={onClose}>
          <svg
              xmlns="http://www.w3.org/2000/svg"
              width="1em"
              height="1em"
              viewBox="0 0 24 24">
            <path
                fill="currentColor"
                d="M6.4 19L5 17.6l5.6-5.6L5 6.4L6.4 5l5.6 5.6L17.6 5L19 6.4L13.4 12l5.6 5.6l-1.4 1.4l-5.6-5.6z"></path>
          </svg>
        </span>

          {/* Search Result */}
          <div>
            {offersLoading || !offers ? <Spinner className="me-2" /> :
                <SearchResult isOpen={isResultOpen} results={offers.data} total={offers.results}/>}
          </div>
        </div>
    );
  }
}

export const categoryOptions = [
  { _id: null, title: 'categories.all' },
  { _id: 'psn', title: 'categories.psn' },
  { _id: 'nintendo', title: 'categories.nintendo' },
  { _id: 'gift-cards', title: 'categories.giftCards' },
  { _id: 'pc-gaming', title: 'categories.pcGaming' },
  { _id: 'skins', title: 'categories.skins' },
  { _id: 'software', title: 'categories.software' },
  { _id: 'vr-games', title: 'categories.vrGames' }
];

export default function MainHeader({
                                     showHeader,
                                     activeLink,
                                     activeCategory,
                                   }) {

  const { t } = useTranslation();
  const {user} = useProfile();
  const {cartData, cartLoading} = useCart();
  const [categoryState, setCategoryState] = useState(null)

  const isVisible = showHeader ?? true;

  const {isSideMenuOpen, OpenSideMenu, CloseSideMenu} =
      useContext(NavContext);

  const [searchInput, setSearchInput] = useState("");

  const [headerBg, setHeaderBg] = useState("");
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [isSearchResultOpen, setIsSearchResultOpen] = useState(false);

  const searchTerm = useDebounce(searchInput, 500);
  const {offers, offersLoading} = useSellerOffers({
    query: generateQuery({
      staticString: `search=${searchTerm}`,
      limit: 5,
      selectedCategory: categoryState,
    })
  });

  function OpenSearchBar() {
    setIsSearchOpen(true);
  }

  function CloseSearchBar() {
    setIsSearchOpen(false);
  }

  function handleSearchInput(e) {
    setSearchInput(e.target.value);
  }

  const location = useLocation();

  useEffect(() => {
    if (searchTerm) setIsSearchResultOpen(true);
    else setIsSearchResultOpen(false);
  }, [searchTerm])

  // Use useEffect to close the menu when the route changes
  useEffect(() => {
    if (isSideMenuOpen) {
      CloseSideMenu(); // Close the menu when the route changes
    }
  }, [location]);

  const threshold = 10; // The scroll height at which the header changes color

  useEffect(() => {
    const scrollableSection = document.getElementById("scrollable-section");

    const handleScroll = () => {
      if (scrollableSection.scrollTop > threshold) {
        setHeaderBg("header_bg"); // Replace with the color you want
      } else {
        setHeaderBg(""); // Original color
      }
    };

    scrollableSection.addEventListener("scroll", handleScroll);

    // Cleanup the event listener when the component unmounts
    return () => {
      scrollableSection.removeEventListener("scroll", handleScroll);
    };
  }, []); // The empty array ensures this effect runs only once

  return (
      <>
        {/* Mobile Header */}
        <div className="mob_header_con d-lg-none">
          <div className="mob_header_cont position-relative">
            <div className="mob_header d-flex align-items-center gap-3">
              <div className="col d-flex gap-3 align-items-center">
                {isSideMenuOpen ? (
                    <span className="mob_header_icon light" onClick={CloseSideMenu}>
                  <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="1em"
                      height="1em"
                      viewBox="0 0 24 24">
                    <path
                        fill="currentColor"
                        d="M6.4 19L5 17.6l5.6-5.6L5 6.4L6.4 5l5.6 5.6L17.6 5L19 6.4L13.4 12l5.6 5.6l-1.4 1.4l-5.6-5.6z"></path>
                  </svg>
                </span>
              ) : (
                <span className="mob_header_icon" onClick={OpenSideMenu}>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="1em"
                    height="1em"
                    viewBox="0 0 24 24">
                    <path
                      fill="currentColor"
                      d="M3 4h18v2H3zm0 7h18v2H3zm0 7h18v2H3z"
                    />
                  </svg>
                </span>
              )}

              <Link to={"/"}>
                <img
                  src="../assets/images/logo2.png"
                  alt=""
                  className="mob_header_img"
                />
              </Link>
            </div>

            {/* Mobile Screen */}
            <div className="col-auto d-flex d-md-none gap-2 align-items-center">
              <span className="mob_header_icon" onClick={OpenSearchBar}>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="1em"
                  height="1em"
                  viewBox="0 0 24 24">
                  <path
                    fill="currentColor"
                    d="m18.031 16.617l4.283 4.282l-1.415 1.415l-4.282-4.283A8.96 8.96 0 0 1 11 20c-4.968 0-9-4.032-9-9s4.032-9 9-9s9 4.032 9 9a8.96 8.96 0 0 1-1.969 5.617m-2.006-.742A6.98 6.98 0 0 0 18 11c0-3.867-3.133-7-7-7s-7 3.133-7 7s3.133 7 7 7a6.98 6.98 0 0 0 4.875-1.975z"></path>
                </svg>
              </span>
              <MobileNotifications />

              <CustomDropdown
                trigger={() =>
                  user ? (
                    <img
                      src={user.avatar || window.origin + "/assets/images/user.png"}
                      alt=""
                      className="header_user_img"
                    />
                  ) : (
                    <span className="mob_header_icon position-relative">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="1em"
                        height="1em"
                        viewBox="0 0 24 24">
                        <path
                          fill="currentColor"
                          d="M12 4a4 4 0 0 1 4 4a4 4 0 0 1-4 4a4 4 0 0 1-4-4a4 4 0 0 1 4-4m0 10c4.42 0 8 1.79 8 4v2H4v-2c0-2.21 3.58-4 8-4"
                        />
                      </svg>
                      <div className="header_user_dot position-absolute"></div>
                    </span>
                  )
                }
                content={ user ? <MobileLoggedIn {...user} /> : <MobileGuest user={!!user} /> }
              />

              <MobileCart user={!!user} cartData={cartData} />
            </div>

            {/* Tablet Screen */}
            <div className="col-auto d-none d-md-flex align-items-center position-relative gap-3">
              <span className="mob_header_icon" onClick={OpenSearchBar}>
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="1em"
                    height="1em"
                    viewBox="0 0 24 24">
                  <path
                      fill="currentColor"
                      d="m18.031 16.617l4.283 4.282l-1.415 1.415l-4.282-4.283A8.96 8.96 0 0 1 11 20c-4.968 0-9-4.032-9-9s4.032-9 9-9s9 4.032 9 9a8.96 8.96 0 0 1-1.969 5.617m-2.006-.742A6.98 6.98 0 0 0 18 11c0-3.867-3.133-7-7-7s-7 3.133-7 7s3.133 7 7 7a6.98 6.98 0 0 0 4.875-1.975z"></path>
                </svg>
              </span>

              <TabNotifications />

              <CustomDropdown
                trigger={(isOpen) => (
                  <div
                    className="header_user d-flex gap-2 align-items-center"
                    role="button">
                    {user ? (
                      <img
                        src={user.avatar || window.origin + "/assets/images/user.png"}
                        alt=""
                        className="header_user_img"
                      />
                    ) : (
                      <svg
                        className="user"
                        xmlns="http://www.w3.org/2000/svg"
                        width="1em"
                        height="1em"
                        viewBox="0 0 24 24">
                        <path
                          fill="currentColor"
                          d="M12 4a4 4 0 0 1 4 4a4 4 0 0 1-4 4a4 4 0 0 1-4-4a4 4 0 0 1 4-4m0 10c4.42 0 8 1.79 8 4v2H4v-2c0-2.21 3.58-4 8-4"
                        />
                      </svg>
                    )}
                    {isOpen ? (
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="1em"
                        height="1em"
                        viewBox="0 0 24 24">
                        <path
                          fill="currentColor"
                          d="M6.4 19L5 17.6l5.6-5.6L5 6.4L6.4 5l5.6 5.6L17.6 5L19 6.4L13.4 12l5.6 5.6l-1.4 1.4l-5.6-5.6z"></path>
                      </svg>
                    ) : (
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="1em"
                        height="1em"
                        viewBox="0 0 24 24">
                        <path
                          fill="currentColor"
                          d="M3 4h18v2H3zm0 7h18v2H3zm0 7h18v2H3z"
                        />
                      </svg>
                    )}
                  </div>
                )}
                content={
                  user ? <WebLoggedIn {...user} /> : <WebGuest user={!!user} />
                }
              />

              <TabCart user={!!user} cartData={cartData} />
            </div>
          </div>

          {/* Mobile Search Box */}
          <MobileSearch isOpen={isSearchOpen} onClose={CloseSearchBar}/>
        </div>

        {isSideMenuOpen ? (
            <div className="mob_menu_con d-flex flex-column position-absolute">
              {user?.role === 'seller' ? (
                  <AccountSideBarContent activeLink={activeLink}/>
              ) : (
                  <MenuSideBarContent
                      activeCategory={activeCategory}
                      activeLink={activeLink}
                  />
              )}
            </div>
        ) : (
            ""
        )}
      </div>

      {/* Main Header */}
        <div
            className={
                headerBg +
                " header_con justify-content-end align-items-center " +
                (isVisible ? "d-none d-lg-flex" : "d-none")
            }>
          <div className="col header_search_con d-flex align-items-center position-relative me-4">
            <div className="col-auto position-relative ps-3 me-2" role="button">
              <CustomDropdown
                  trigger={(isOpen) => (
                      <p
                          className={
                              "header_search_sel d-flex gap-2 align-items-center " +
                              (isOpen ? "text-white" : "")
                          }>
                    <span className={"icon " + (isOpen ? "text-white" : "")}>
                      <svg
                          className=" me-2"
                          xmlns="http://www.w3.org/2000/svg"
                          width="1em"
                          height="1em"
                          viewBox="0 0 24 24">
                        <g fill="none" fillRule="evenodd">
                          <path
                              d="m12.593 23.258l-.011.002l-.071.035l-.02.004l-.014-.004l-.071-.035q-.016-.005-.024.005l-.004.01l-.017.428l.005.02l.01.013l.104.074l.015.004l.012-.004l.104-.074l.012-.016l.004-.017l-.017-.427q-.004-.016-.017-.018m.265-.113l-.013.002l-.185.093l-.01.01l-.003.011l.018.43l.005.012l.008.007l.201.093q.019.005.029-.008l.004-.014l-.034-.614q-.005-.018-.02-.022m-.715.002a.02.02 0 0 0-.027.006l-.006.014l-.034.614q.001.018.017.024l.015-.002l.201-.093l.01-.008l.004-.011l.017-.43l-.003-.012l-.01-.01z"/>
                          <path
                              fill="currentColor"
                              d="M3 5a2 2 0 0 1 2-2h6v8H3zm8 8H3v6a2 2 0 0 0 2 2h6zm2 8h6a2 2 0 0 0 2-2v-6h-8zm0-10V3h6a2 2 0 0 1 2 2v6z"
                          />
                        </g>
                      </svg>
                    </span>
                        View All Games
                        <span className={"icon " + (isOpen ? "rotate" : "")}>
                      <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="1em"
                          height="1em"
                          viewBox="0 0 24 24">
                        <path
                            fill="none"
                            stroke="currentColor"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="m19 9l-7 7l-7-7"></path>
                      </svg>
                    </span>
                      </p>
                  )}
                  content={
                    <div className="header_drop_cont sel position-absolute">
                      {categoryOptions.map(item => <p
                          key={item.title}
                          className={`header_drop_cate ${item._id === categoryState ? 'active' : ''} d-flex gap-2 align-items-center position-relative`}
                          onClick={()=> setCategoryState(item._id)}
                          role="button">
                        {item._id === categoryState && getCheckIcon()}
                        {t(item.title)}
                      </p>)}
                    </div>
                  }
              />
            </div>
            <div className="col">
              <input
                  type="text"
                  className="header_search"
                  placeholder={t('search.placeholder')}
                  value={searchInput}
                  onChange={handleSearchInput}
              />
            </div>
            {/* Search Result */}
            {offersLoading || !offers ? <Spinner className="me-2" /> :
                <SearchResult isOpen={isSearchResultOpen} results={offers.data} total={offers.total} searchTerm={searchTerm}/>}
          </div>

          <div className="col-auto d-flex align-items-center position-relative">
            <Notifications />

            <CustomDropdown
                trigger={(isOpen) => (
                    <div
                        className="header_user d-flex gap-2 align-items-center me-3"
                        role="button">
                      {user ? (
                          <img
                              src={user.avatar || window.origin + "/assets/images/user.png"}
                              alt=""
                              className="header_user_img"
                          />
                      ) : (
                          <svg
                              className="user"
                              xmlns="http://www.w3.org/2000/svg"
                              width="1em"
                              height="1em"
                              viewBox="0 0 24 24">
                            <path
                                fill="currentColor"
                                d="M12 4a4 4 0 0 1 4 4a4 4 0 0 1-4 4a4 4 0 0 1-4-4a4 4 0 0 1 4-4m0 10c4.42 0 8 1.79 8 4v2H4v-2c0-2.21 3.58-4 8-4"
                            />
                          </svg>
                      )}
                      {isOpen ? (
                          <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="1em"
                              height="1em"
                              viewBox="0 0 24 24">
                            <path
                                fill="currentColor"
                                d="M6.4 19L5 17.6l5.6-5.6L5 6.4L6.4 5l5.6 5.6L17.6 5L19 6.4L13.4 12l5.6 5.6l-1.4 1.4l-5.6-5.6z"></path>
                          </svg>
                      ) : (
                          <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="1em"
                              height="1em"
                              viewBox="0 0 24 24">
                            <path
                                fill="currentColor"
                                d="M3 4h18v2H3zm0 7h18v2H3zm0 7h18v2H3z"
                            />
                          </svg>
                      )}
                    </div>
                )}
                content={user ? <WebLoggedIn {...user}/> : <WebGuest user={!!user} />}>

            </CustomDropdown>

            <Link to={"/cart"}>
              <div className="header_cart active d-flex align-items-center position-relative">
                <svg
                    className="me-1"
                    xmlns="http://www.w3.org/2000/svg"
                    width="1em"
                    height="1em"
                    viewBox="0 0 24 24">
                  <path
                      fill="currentColor"
                      fillRule="evenodd"
                      d="M14.665 2.33a.75.75 0 0 1 1.006.335l2.201 4.402c1.353.104 2.202.37 2.75 1.047c.9 1.114.541 2.79-.177 6.143l-.429 2c-.487 2.273-.73 3.409-1.555 4.076S16.474 21 14.15 21h-4.3c-2.324 0-3.486 0-4.31-.667c-.826-.667-1.07-1.803-1.556-4.076l-.429-2c-.718-3.353-1.078-5.029-.177-6.143c.548-.678 1.397-.943 2.75-1.047l2.201-4.402a.75.75 0 0 1 1.342.67l-1.835 3.67Q8.559 7 9.422 7h5.156q.863-.001 1.586.005l-1.835-3.67a.75.75 0 0 1 .336-1.006M7.25 12a.75.75 0 0 1 .75-.75h8a.75.75 0 0 1 0 1.5H8a.75.75 0 0 1-.75-.75M10 14.25a.75.75 0 0 0 0 1.5h4a.75.75 0 0 0 0-1.5z"
                      clipRule="evenodd"
                  />
                </svg>
                {user && <>
                  {(!cartLoading && cartData) ? <>
                    ${cartData.summary.totalCustomerPays}
                    {cartData.items.length > 0 && <span
                        className="header_cart_num d-flex justify-content-center align-items-center position-absolute">
                          {cartData.items.length}
                    </span>}
                  </> : <Spinner />}
                </>}
              </div>
            </Link>
          </div>
        </div>
      </>
  );
}
