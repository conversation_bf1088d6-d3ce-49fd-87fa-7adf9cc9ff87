# Complete Translation Setup Guide

## Overview

Your ecommerce application now has a complete translation system that combines:

1. **i18next + react-i18next** - For static UI text (buttons, labels, navigation)
2. **LibreTranslate** - For dynamic content from database (product descriptions, blog posts, etc.)

## Features Implemented

✅ **Static Content Translation**
- All UI elements (buttons, labels, navigation, forms)
- 5 languages: English, Spanish, French, Latvian, Polish
- Instant language switching with localStorage persistence

✅ **Dynamic Content Translation**
- Product descriptions, blog posts, FAQ content
- Real-time translation using LibreTranslate API
- Caching system to avoid repeated API calls
- Rate limiting to respect API limits

✅ **Language Dropdown**
- Functional language selector in footer
- Visual feedback for current language
- Persistent language preference

✅ **Error Handling & Loading States**
- Graceful fallbacks when translation fails
- Loading indicators during translation
- Error boundaries for translation components

## How to Use

### 1. Static Text Translation

```jsx
import { useTranslation } from '../../hooks/translation/useTranslation';

function MyComponent() {
  const { t } = useTranslation();
  
  return (
    <div>
      <h1>{t('navigation.home')}</h1>
      <button>{t('shop.addToCart')}</button>
      <p>{t('common.loading')}</p>
    </div>
  );
}
```

### 2. Dynamic Content Translation

```jsx
import { useTranslatedData } from '../../hooks/translation/useTranslatedData';

function ProductList() {
  const { products } = useProducts(); // Your existing hook
  const { data: translatedProducts, isTranslating } = useTranslatedData(
    products, 
    ['name', 'description'] // Fields to translate
  );
  
  return (
    <div>
      {isTranslating && <p>Translating...</p>}
      {translatedProducts?.map(product => (
        <div key={product.id}>
          <h3>{product.name}</h3>
          <p>{product.description}</p>
        </div>
      ))}
    </div>
  );
}
```

### 3. Specific Content Hooks

```jsx
// For FAQ
import { useTranslatedFAQ } from '../../hooks/translation/useTranslatedData';
const { data: translatedFaq, isTranslating } = useTranslatedFAQ(faqData);

// For Blog Posts
import { useTranslatedBlogs } from '../../hooks/translation/useTranslatedData';
const { data: translatedBlogs, isTranslating } = useTranslatedBlogs(blogData);

// For Categories
import { useTranslatedCategories } from '../../hooks/translation/useTranslatedData';
const { data: translatedCategories, isTranslating } = useTranslatedCategories(categoryData);
```

## Translation Keys Structure

```
navigation.*     - Navigation items (home, cart, account, etc.)
footer.*         - Footer content (copyright, privacy, terms)
common.*         - Common UI elements (buttons, loading, error states)
forms.*          - Form labels and validation messages
shop.*           - Shopping related text (add to cart, price, etc.)
cart.*           - Shopping cart related text
order.*          - Order related text
blog.*           - Blog related text
help.*           - Help center text
faq.*            - FAQ related text
messages.*       - Success/error messages
```

## LibreTranslate Configuration

### Free Public Instance (Current Setup)
- URL: `https://libretranslate.de/translate`
- Rate limited but free
- No API key required

### Self-Hosted Option (Recommended for Production)

1. Install LibreTranslate:
```bash
pip install libretranslate
libretranslate --host 0.0.0.0 --port 5000
```

2. Update the API URL in `src/services/translationService.js`:
```javascript
this.apiUrl = 'http://your-server:5000/translate';
```

## Adding New Languages

1. **Add translation file:**
```bash
# Create new language file
src/i18n/locales/de/common.json  # For German
```

2. **Update i18n config:**
```javascript
// In src/i18n/index.js
import deTranslations from './locales/de/common.json';

const resources = {
  // ... existing languages
  de: { common: deTranslations }
};
```

3. **Update language dropdown:**
```javascript
// In src/contexts/TranslationContext.jsx
const languageMap = {
  // ... existing languages
  de: { code: 'de', name: 'Deutsch', flag: '🇩🇪' }
};
```

## Performance Optimization

### Translation Caching
- Translations are cached in memory
- Cache persists during session
- Automatic cache cleanup when limit reached

### Rate Limiting
- 1 second delay between API requests
- Retry mechanism with exponential backoff
- Queue system for batch translations

### Best Practices

1. **Use specific translation hooks:**
```jsx
// Good
const { data: translatedFaq } = useTranslatedFAQ(faq);

// Instead of
const { translateFAQs } = useTranslation();
```

2. **Handle loading states:**
```jsx
{isTranslating ? <Spinner /> : <Content />}
```

3. **Provide fallbacks:**
```jsx
const displayText = translatedText || originalText;
```

## Testing Translation

1. **Change language in footer dropdown**
2. **Check static text changes immediately**
3. **Dynamic content translates with loading indicator**
4. **Language preference persists on page reload**

## Troubleshooting

### Translation Not Working
1. Check browser console for errors
2. Verify LibreTranslate API is accessible
3. Check if language is supported

### Slow Translations
1. Consider self-hosting LibreTranslate
2. Implement more aggressive caching
3. Reduce text length for translation

### Missing Translations
1. Add missing keys to translation files
2. Use fallback text in components
3. Check translation key spelling

## Files Modified/Created

### New Files:
- `src/i18n/index.js` - i18n configuration
- `src/i18n/locales/*/common.json` - Translation files
- `src/contexts/TranslationContext.jsx` - Translation context
- `src/hooks/translation/useTranslation.js` - Main translation hook
- `src/hooks/translation/useDynamicTranslation.js` - Dynamic translation hook
- `src/hooks/translation/useTranslatedData.js` - Data translation hooks
- `src/services/translationService.js` - LibreTranslate service
- `src/components/translation/` - Translation components

### Modified Files:
- `src/main.jsx` - Added i18n initialization and TranslationProvider
- `src/pages/componets/MainFooter.jsx` - Added functional language dropdown
- `src/components/faq/FAQ.jsx` - Example of translated component

## Next Steps

1. **Update more components** to use translation keys
2. **Add more languages** as needed
3. **Consider self-hosting LibreTranslate** for production
4. **Implement translation management** for content editors
5. **Add SEO optimization** for translated content
