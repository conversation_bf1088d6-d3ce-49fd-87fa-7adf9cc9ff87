import React from 'react';
import Spinner from '../common/Spinner';

const TranslationLoader = ({ 
  isTranslating, 
  children, 
  loadingText = "Translating...", 
  showSpinner = true,
  inline = false 
}) => {
  if (isTranslating) {
    if (inline) {
      return (
        <span className="text-muted">
          {showSpinner && <span className="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>}
          {loadingText}
        </span>
      );
    }
    
    return (
      <div className="text-center p-3">
        {showSpinner && <Spinner />}
        <p className="text-muted mt-2">{loadingText}</p>
      </div>
    );
  }

  return children;
};

export default TranslationLoader;
