import { useState, useCallback } from 'react';
import { useTranslationContext } from '../../contexts/TranslationContext';

// Mock translation data for testing
const mockTranslations = {
  es: {
    'Gaming Headset Pro': 'Auriculares Gaming Pro',
    'High-quality wireless gaming headset': 'Auriculares gaming inalámbricos de alta calidad',
    'Perfect for competitive gaming': 'Perfecto para gaming competitivo',
    'Digital Download': 'Descarga Digital',
    'Instant Delivery': 'Entrega Instantánea',
    'PlayStation Network': 'Red PlayStation',
    'Nintendo Switch': 'Nintendo Switch',
    'Gift Card': 'Tarjeta de Regalo',
    'Software License': 'Licencia de Software',
    'Game Key': 'Clave de Juego',
    'CD Key': 'Clave CD',
    'Digital Key': 'Clave Digital',
    'Available Now': 'Disponible Ahora',
    'Out of Stock': 'Agotado',
    'In Stock': 'En Stock',
    'Best Seller': 'Más Vendido',
    'New Release': 'Nuevo Lanzamiento',
    'Limited Time Offer': 'Oferta por Tiempo Limitado'
  },
  fr: {
    'Gaming Headset Pro': 'Casque Gaming Pro',
    'High-quality wireless gaming headset': 'Casque gaming sans fil de haute qualité',
    'Perfect for competitive gaming': 'Parfait pour le gaming compétitif',
    'Digital Download': 'Téléchargement Numérique',
    'Instant Delivery': 'Livraison Instantanée',
    'PlayStation Network': 'Réseau PlayStation',
    'Nintendo Switch': 'Nintendo Switch',
    'Gift Card': 'Carte Cadeau',
    'Software License': 'Licence Logiciel',
    'Game Key': 'Clé de Jeu',
    'CD Key': 'Clé CD',
    'Digital Key': 'Clé Numérique',
    'Available Now': 'Disponible Maintenant',
    'Out of Stock': 'Rupture de Stock',
    'In Stock': 'En Stock',
    'Best Seller': 'Meilleure Vente',
    'New Release': 'Nouvelle Sortie',
    'Limited Time Offer': 'Offre Limitée'
  },
  lv: {
    'Gaming Headset Pro': 'Spēļu Austiņas Pro',
    'High-quality wireless gaming headset': 'Augstas kvalitātes bezvadu spēļu austiņas',
    'Perfect for competitive gaming': 'Ideāls konkurētspējīgām spēlēm',
    'Digital Download': 'Digitālā Lejupielāde',
    'Instant Delivery': 'Tūlītēja Piegāde',
    'PlayStation Network': 'PlayStation Tīkls',
    'Nintendo Switch': 'Nintendo Switch',
    'Gift Card': 'Dāvanu Karte',
    'Software License': 'Programmatūras Licence',
    'Game Key': 'Spēles Atslēga',
    'CD Key': 'CD Atslēga',
    'Digital Key': 'Digitālā Atslēga',
    'Available Now': 'Pieejams Tagad',
    'Out of Stock': 'Nav Noliktavā',
    'In Stock': 'Noliktavā',
    'Best Seller': 'Vispārdotākais',
    'New Release': 'Jaunums',
    'Limited Time Offer': 'Ierobežota Laika Piedāvājums'
  },
  pl: {
    'Gaming Headset Pro': 'Słuchawki Gamingowe Pro',
    'High-quality wireless gaming headset': 'Wysokiej jakości bezprzewodowe słuchawki gamingowe',
    'Perfect for competitive gaming': 'Idealne do gier konkurencyjnych',
    'Digital Download': 'Pobieranie Cyfrowe',
    'Instant Delivery': 'Natychmiastowa Dostawa',
    'PlayStation Network': 'Sieć PlayStation',
    'Nintendo Switch': 'Nintendo Switch',
    'Gift Card': 'Karta Podarunkowa',
    'Software License': 'Licencja Oprogramowania',
    'Game Key': 'Klucz Gry',
    'CD Key': 'Klucz CD',
    'Digital Key': 'Klucz Cyfrowy',
    'Available Now': 'Dostępne Teraz',
    'Out of Stock': 'Brak w Magazynie',
    'In Stock': 'W Magazynie',
    'Best Seller': 'Bestseller',
    'New Release': 'Nowość',
    'Limited Time Offer': 'Oferta Czasowa'
  }
};

export const useMockTranslation = () => {
  const { currentLanguage } = useTranslationContext();
  const [isTranslating, setIsTranslating] = useState(false);

  const translateText = useCallback(async (text, targetLanguage = currentLanguage, sourceLanguage = 'en') => {
    // Return original text if target language is English or same as source
    if (targetLanguage === 'en' || targetLanguage === sourceLanguage) {
      return text;
    }

    // Skip translation for empty or very short text
    if (!text || text.trim().length < 2) {
      return text;
    }

    setIsTranslating(true);

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300));

    try {
      const translations = mockTranslations[targetLanguage];
      if (translations) {
        // Try exact match first
        if (translations[text]) {
          setIsTranslating(false);
          return translations[text];
        }

        // Try partial matches for longer text
        for (const [key, value] of Object.entries(translations)) {
          if (text.toLowerCase().includes(key.toLowerCase())) {
            const translated = text.replace(new RegExp(key, 'gi'), value);
            setIsTranslating(false);
            return translated;
          }
        }
      }

      // If no translation found, return original text
      setIsTranslating(false);
      return text;
    } catch (error) {
      console.error('Mock translation error:', error);
      setIsTranslating(false);
      return text;
    }
  }, [currentLanguage]);

  const translateObject = useCallback(async (obj, fieldsToTranslate = [], targetLanguage = currentLanguage, sourceLanguage = 'en') => {
    if (!obj || typeof obj !== 'object') {
      return obj;
    }

    const translatedObj = { ...obj };

    for (const field of fieldsToTranslate) {
      if (obj[field] && typeof obj[field] === 'string') {
        translatedObj[field] = await translateText(obj[field], targetLanguage, sourceLanguage);
      }
    }

    return translatedObj;
  }, [translateText, currentLanguage]);

  const translateObjectArray = useCallback(async (array, fieldsToTranslate = [], targetLanguage = currentLanguage, sourceLanguage = 'en') => {
    if (!Array.isArray(array)) {
      return array;
    }

    const translatedArray = await Promise.all(
      array.map(item => translateObject(item, fieldsToTranslate, targetLanguage, sourceLanguage))
    );

    return translatedArray;
  }, [translateObject, currentLanguage]);

  return {
    translateText,
    translateObject,
    translateObjectArray,
    isTranslating,
    currentLanguage,
  };
};
