import React from 'react';

class TranslationErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('Translation Error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="alert alert-warning" role="alert">
          <h6>Translation Error</h6>
          <p>There was an issue with translation. Showing original content.</p>
          {this.props.fallback || this.props.children}
        </div>
      );
    }

    return this.props.children;
  }
}

export default TranslationErrorBoundary;
