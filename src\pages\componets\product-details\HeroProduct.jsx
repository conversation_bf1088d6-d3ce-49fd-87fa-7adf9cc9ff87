import {<PERSON>, useParams} from "react-router-dom";
import {Swiper, SwiperSlide} from "swiper/react";
import {Navigation, Pagination} from "swiper/modules";
import MessageModal from "../modals/ProductMessage.jsx";
import {useContext, useState} from "react";
import {useOfferDetails} from "../../../hooks/offers/useOfferDetails.js";
import {calculateSavingsPercentage} from "../../../vbrae-utils/lib/misc.js";
import {useAddCart} from "../../../hooks/cart/useAddCart.js";
import {ModalContext} from "../../../store/ModalContext.jsx";
import {getAccessToken} from "../../../vbrae-utils/index.js";
import Spinner from "../../../components/common/Spinner.jsx";
import { useTranslation } from "../../../hooks/translation/useTranslation";

// eslint-disable-next-line react/prop-types
function ProductImage({coverImage, category}) {

    return (
        <>
            <div className="col details_img_con position-relative mb-3">
        <span
            className="detail_img_star position-absolute d-flex justify-content-center align-items-center"
            role="button">
          <svg
              xmlns="http://www.w3.org/2000/svg"
              width="1em"
              height="1em"
              viewBox="0 0 24 24">
            <path
                fill="currentColor"
                d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2L9.19 8.63L2 9.24l5.46 4.73L5.82 21z"
            />
          </svg>
        </span>

                <Swiper
                    modules={[Navigation, Pagination]}
                    spaceBetween={10}
                    slidesPerView={1}
                    // navigation={true}
                    onSwiper={(swiper) => {
                        swiper.wrapperEl.classList.add("game_slider_con");
                    }}
                    pagination={{
                        // el: "",
                        clickable: true,
                        renderBullet: function (index, className) {
                            // Customize the pagination bullets with your own HTML structure and classes
                            return `<span class="${className} custom_bullet"></span>`;
                        },
                    }}>
                    <SwiperSlide>
                        <img
                            src={coverImage}
                            alt=""
                            className="details_img"
                        />
                    </SwiperSlide>
                </Swiper>
            </div>
            <div className="col d-flex flex-wrap gap-2 gap-md-4 mb-3 mb-lg-0">
                <div className="col-12 col-md">
                    <label htmlFor="platform" className="details_hd mb-2">
                        {t('shop.platform')}
                    </label>
                    <select name="" className="details_sel">
                        <option value="">{(originalOffer?.data.category || displayOffer.category)}</option>
                    </select>
                </div>

                <div className="col-12 col-md">
                    {category.name !== "SOFTWARE" && <>
                        <label htmlFor="platform" className="details_hd mb-2">
                            Edition
                        </label>
                        <select name="" className="details_sel">
                            <option value="">Standard</option>
                        </select>
                    </>}
                </div>
            </div>
        </>
    );
}

/* eslint-disable react/prop-types */
export default function HeroProduct({onClick, fromClick, offer: translatedOffer, isTranslating, ...otherProps}) {
    const { t } = useTranslation();
    const {OpenModal} = useContext(ModalContext);
    const {id} = useParams();

    const hasUser = !!getAccessToken();

    const [openMessage, setOpenMessage] = useState(false);

    const {offer: originalOffer, offerLoading} = useOfferDetails({_id: id});
    const {cartLoading, cartRefetch} = useAddCart({quantity: 1, offerId: id});

    // Use the passed offer (translated) or fall back to original
    const displayOffer = offer || originalOffer?.data;

    function CloseMessageModal() {
        setOpenMessage(false);
    }

    function OpenMessageModal() {
        if(!hasUser) return OpenModal("login");
        setOpenMessage(true);
    }

    const handleCartClick = () => {
        if(hasUser) cartRefetch().finally();
        else OpenModal("login")
    }

    if(offerLoading || !displayOffer) {
        return <Spinner className="my-5" />
    }

    const splitPrice = (originalOffer?.data.customerPays || displayOffer.customerPays).toString().split(".");
    const splitGenre = (originalOffer?.data.template.genres || displayOffer.template?.genres) ? (originalOffer?.data.template.genres || displayOffer.template?.genres).split(",") : [];
    const isCountryAllowed = (originalOffer?.data.region || displayOffer.region) === 'Global' || (originalOffer?.data.region || displayOffer.region) === "Switzerland";
    const hasDigitalKey = originalOffer?.data.instantDelivery || displayOffer.instantDelivery;

    const relatedOffers = originalOffer?.relatedOffers?.length > 0;

    return (
        <>
            <MessageModal isOpen={openMessage} onClose={CloseMessageModal} {...otherProps} />
            <div className="col d-flex gap-4 gap-xl-5 mb-4 mb-lg-5">
                <div className="col-5 d-none d-lg-block">
                    <ProductImage {...(originalOffer?.data.template || displayOffer.template)}/>
                </div>
                <div className="col-12 col-lg">
                    {(originalOffer?.data.template.dlc || displayOffer.template?.dlc) && <div className="d-flex flex-wrap">
                        <span className="game_badge type1 me-1 mb-1">DLC</span>
                    </div>}

                    <h4 className="details_header">
                        {isTranslating ? (originalOffer?.data.name || displayOffer.name) : (displayOffer.name || originalOffer?.data.name)}
                        <span className="text-capitalize"> {originalOffer?.data.category || displayOffer.category}</span>
                        {(originalOffer?.data.instantDelivery || displayOffer.instantDelivery) ? ` ${t('shop.digitalKey')} ` : ` ${t('shop.cdKey')} `}
                        <span className="text-uppercase">{originalOffer?.data.region || displayOffer.region}</span>
                    </h4>

                    <div className="d-flex align-items-center gap-2 mb-3">
                      <span className="details_review text-white">
                        <svg
                            className="me-1"
                            xmlns="http://www.w3.org/2000/svg"
                            width="1em"
                            height="1em"
                            viewBox="0 0 24 24">
                          <path
                              fill="currentColor"
                              d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2L9.19 8.63L2 9.24l5.46 4.73L5.82 21z"
                          />
                        </svg>
                        <strong> {offer.data.averageRating.toFixed(1)}</strong>
                      </span>
                        <span className="details_review" onClick={onClick}>
                        <strong>• {offer.data.totalRatings} </strong>
                        Reviews
                        </span>

                        <span className="details_review_tag d-flex align-items-center gap-1">
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="1em"
                            height="1em"
                            viewBox="0 0 24 24">
                          <path
                              fill="currentColor"
                              d="M11.99 0A12 12 0 1 0 24 12v-.014A12 12 0 0 0 11.99 0m-.055 2.564a9.4 9.4 0 0 1 9.407 9.389v.01a9.399 9.399 0 1 1-9.408-9.399Zm-1.61 17.198l2.046-2.046l-3.94-3.94c-.165-.166-.345-.373-.442-.608c-.221-.47-.318-1.203.221-1.742c.664-.664 1.548-.387 2.406.47l3.788 3.788l2.046-2.046l-3.954-3.954a2.5 2.5 0 0 1-.456-.622c-.263-.539-.25-1.216.235-1.7c.677-.678 1.562-.429 2.544.553l3.677 3.677l2.046-2.046l-3.982-3.982c-2.018-2.018-3.912-1.949-5.212-.65c-.498.499-.802 1.024-.954 1.618a4 4 0 0 0-.055 1.686l-.027.028c-.996-.414-2.13-.166-3 .705c-1.162 1.161-1.12 2.392-.982 3.11l-.042.043l-1.009-.816l-1.77 1.77a64 64 0 0 1 2.213 2.1z"
                          />
                        </svg>
                        33
                      </span>
                    </div>

                    {/* Product Image for Small Screen */}
                    <div className="col d-lg-none">
                        <ProductImage {...offer.data.template}/>
                    </div>

                    <div className="d-flex gap-2 mb-3">
                      <span className="game_badge type5">
                        <svg
                            className="me-1"
                            xmlns="http://www.w3.org/2000/svg"
                            width="1em"
                            height="1em"
                            viewBox="0 0 24 24">
                          <path
                              fill="currentColor"
                              d="M12 8H4a2 2 0 0 0-2 2v4a2 2 0 0 0 2 2h1v4a1 1 0 0 0 1 1h2a1 1 0 0 0 1-1v-4h3l5 4V4zm9.5 4c0 1.71-.96 3.26-2.5 4V8c1.53.75 2.5 2.3 2.5 4"
                          />
                        </svg>
                        Promoted
                      </span>
                        <span className="game_badge type2">
                        <svg
                            className="me-1"
                            xmlns="http://www.w3.org/2000/svg"
                            width="1em"
                            height="1em"
                            viewBox="0 0 256 256">
                          <path
                              fill="currentColor"
                              d="M143.38 17.85a8 8 0 0 0-12.63 3.41l-22 60.41l-24.16-23.41a8 8 0 0 0-11.93.89C51 87.53 40 116.08 40 144a88 88 0 0 0 176 0c0-59.45-50.79-108-72.62-126.15m40.51 135.49a57.6 57.6 0 0 1-46.56 46.55a7.7 7.7 0 0 1-1.33.11a8 8 0 0 1-1.32-15.89c16.57-2.79 30.63-16.85 33.44-33.45a8 8 0 0 1 15.78 2.68Z"
                          />
                        </svg>
                        Hot
                      </span>
                        {offer.data.instantDelivery && <span className="game_badge type3">
                        <svg
                            className="me-1"
                            xmlns="http://www.w3.org/2000/svg"
                            width="1em"
                            height="1em"
                            viewBox="0 0 16 16">
                          <path
                              fill="currentColor"
                              d="M11.251.068a.5.5 0 0 1 .227.58L9.677 6.5H13a.5.5 0 0 1 .364.843l-8 8.5a.5.5 0 0 1-.842-.49L6.323 9.5H3a.5.5 0 0 1-.364-.843l8-8.5a.5.5 0 0 1 .615-.09z"
                          />
                        </svg>
                        Instant
                      </span>}
                    </div>
                    <div className="col d-flex gap-2 mb-3">
                        <Link to={`/shop-details/${(originalOffer?.data.shop._id || displayOffer.shop?._id)}?seller=${(originalOffer?.data.seller._id || displayOffer.seller?._id)}`}>
                            <div className=" position-relative">
                                <img
                                    src={"./assets/images/icons/verify.svg"}
                                    alt=""
                                    className="details_profile_verify position-absolute"
                                />
                                <img
                                    src={(originalOffer?.data.seller.avatar || displayOffer.seller?.avatar) || `${window.origin}/assets/images/user.png`}
                                    alt=""
                                    className="details_profile_img"
                                />
                            </div>
                        </Link>
                        <div className="col">
                            <div className="d-flex flex-wrap gap-3 align-items-start">
                                <p className="details_profile_name">
                                    {t('shop.seller')}
                                    <Link to={`/shop-details/${(originalOffer?.data.shop._id || displayOffer.shop?._id)}?seller=${(originalOffer?.data.seller._id || displayOffer.seller?._id)}`}>
                                        <span className="text-white"> {originalOffer?.data.seller.name || displayOffer.seller?.name}</span>
                                    </Link>
                                    <br/>
                                    {(originalOffer?.data.seller.sellerStats.totalOrders || displayOffer.seller?.sellerStats?.totalOrders)} {t('shop.orders')}
                                </p>
                                    <Link to={`/shop-details/${offer.data.shop._id}?seller=${offer.data.seller._id}`}>
                                        <span
                                        className="details_profile_tag position-relative d-flex align-items-center">
                                          <svg
                                              className="position-absolute"
                                              xmlns="http://www.w3.org/2000/svg"
                                              width="1em"
                                              height="1em"
                                              viewBox="0 0 24 24">
                                            <path
                                                fill="currentColor"
                                                d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2L9.19 8.63L2 9.24l5.46 4.73L5.82 21z"
                                            />
                                          </svg>
                                            {offer.data.seller.sellerStats.averageRating.toFixed(1)}
                                        </span>
                                    </Link>


                                <span
                                    className={`details_profile_tag seller position-relative d-flex align-items-center`}>
                            <svg
                                className="position-absolute"
                                xmlns="http://www.w3.org/2000/svg"
                                width="1em"
                                height="1em"
                                viewBox="0 0 256 256">
                              <path
                                  fill="currentColor"
                                  d="M239.75 90.81c0 .11 0 .21-.07.32L217 195a16 16 0 0 1-15.72 13H54.71A16 16 0 0 1 39 195L16.32 91.13c0-.11-.05-.21-.07-.32A16 16 0 0 1 44 77.39l33.67 36.29l35.8-80.29a1 1 0 0 0 0-.1a16 16 0 0 1 29.06 0a1 1 0 0 0 0 .1l35.8 80.29L212 77.39a16 16 0 0 1 27.71 13.42Z"
                              />
                            </svg>
                                    {offer.data.seller.sellerStats.tier}
                          </span>
                            </div>
                            {/* <p className="details_profile_name">474 Orders</p> */}
                        </div>
                    </div>

                    <div className="d-flex gap-3 align-items-center mb-3">
                        <p className="details_price mb-0">
                            ${splitPrice[0]}<span className={splitPrice[1] ? '' : 'd-none'}>.{splitPrice[1]}</span>
                        </p>
                        <span className="details_price_tag">{calculateSavingsPercentage(offer.data.expectedPrice, offer.data.customerPays)}%</span>
                        <p className="details_price_sm mb-0">${offer.data.expectedPrice}</p>
                    </div>

                    <div className="col d-flex flex-wrap gap-2 align-items-center mb-3">
                        {cartLoading ? <Spinner /> :
                            <button onClick={handleCartClick}
                                    type="button"
                                    className="col-auto details_button d-flex gap-2 align-items-center">
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="1em"
                                    height="1em"
                                    viewBox="0 0 24 24">
                                    <path
                                        fill="currentColor"
                                        fillRule="evenodd"
                                        d="M14.665 2.33a.75.75 0 0 1 1.006.335l2.201 4.402c1.353.104 2.202.37 2.75 1.047c.9 1.114.541 2.79-.177 6.143l-.429 2c-.487 2.273-.73 3.409-1.555 4.076S16.474 21 14.15 21h-4.3c-2.324 0-3.486 0-4.31-.667c-.826-.667-1.07-1.803-1.556-4.076l-.429-2c-.718-3.353-1.078-5.029-.177-6.143c.548-.678 1.397-.943 2.75-1.047l2.201-4.402a.75.75 0 0 1 1.342.67l-1.835 3.67Q8.559 7 9.422 7h5.156q.863-.001 1.586.005l-1.835-3.67a.75.75 0 0 1 .336-1.006M7.25 12a.75.75 0 0 1 .75-.75h8a.75.75 0 0 1 0 1.5H8a.75.75 0 0 1-.75-.75M10 14.25a.75.75 0 0 0 0 1.5h4a.75.75 0 0 0 0-1.5z"
                                        clipRule="evenodd"
                                    />
                                </svg>
                                {t('shop.addToCart')}
                            </button>}

                        <div
                            className="col-auto details_mail d-flex justify-content-center align-items-center"
                            role="button"
                            onClick={() => OpenMessageModal()}>
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="1em"
                                height="1em"
                                viewBox="0 0 24 24">
                                <g fill="currentColor">
                                    <path
                                        d="M1.5 8.67v8.58a3 3 0 0 0 3 3h15a3 3 0 0 0 3-3V8.67l-8.928 5.493a3 3 0 0 1-3.144 0z"/>
                                    <path
                                        d="M22.5 6.908V6.75a3 3 0 0 0-3-3h-15a3 3 0 0 0-3 3v.158l9.714 5.978a1.5 1.5 0 0 0 1.572 0z"/>
                                </g>
                            </svg>
                        </div>

                        {relatedOffers && offer.data.category !== 'software' && <p className=" details_off">
                            {relatedOffers} MORE OFFER{relatedOffers > 1 ? 'S' : ''} AVAILABLE STARTING <span onClick={fromClick}>FROM ${offer.lowestCustomerPays}</span>
                        </p>}
                    </div>

                    <hr className="details_hr mb-4"/>

                    <div className="col">
                        {offer.data.region !== 'Global' && <p className="details_hint d-flex gap-1 align-items-center mb-2">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="1em"
                                height="1em"
                                viewBox="0 0 24 24">
                                <path
                                    fill="currentColor"
                                    d="M11 17h2v-6h-2zm1-8q.425 0 .713-.288T13 8t-.288-.712T12 7t-.712.288T11 8t.288.713T12 9m0 13q-2.075 0-3.9-.788t-3.175-2.137T2.788 15.9T2 12t.788-3.9t2.137-3.175T8.1 2.788T12 2t3.9.788t3.175 2.137T21.213 8.1T22 12t-.788 3.9t-2.137 3.175t-3.175 2.138T12 22"
                                />
                            </svg>
                            This product is region / country restricted to: OTHER.
                            To see the list of supported countries, click here.
                        </p>}

                        <div className="col mb-3">
                            <div className="details_text d-md-flex gap-2 mb-2">
                                {offer.data.region && <span>{offer.data.region}: </span>}
                                <p className="mb-0">
                                    Can {isCountryAllowed ? '' : 'not'} be activated in <strong
                                    className="fw-bold">Switzerland</strong>.
                                    Check region <u>restrictions</u>
                                </p>
                            </div>
                            {hasDigitalKey && <div className="details_text d-md-flex gap-2 mb-2">
                                <span>Digital Key: </span>
                                <p className="mb-0">This is a digital edition of the product (CD-KEY)</p>
                            </div>}
                        </div>

                        <div className="col d-flex flex-wrap gap-4">
                        {splitGenre.length > 0 && <div className="col-auto">
                                <p className="details_hd mb-2">Genres</p>
                                <div className="d-flex flex-wrap gap-2">
                                    {splitGenre.map(item => <span key={item} className="details_hd_tag">abc</span>)}
                                </div>
                            </div>}
                            {offer.data.template.languages.length > 0 && <div className="col-auto w-100">
                                <p className="details_hd mb-2">Languages</p>
                                <div className="d-flex flex-wrap gap-2">
                                    {offer.data.template.languages.map((item, index) => (
                                        <span key={index} className="details_hd_tag">{item}</span>
                                    ))}
                                </div>
                            </div>}
                        </div>
                    </div>
                </div>
            </div>
        </>
    )
}