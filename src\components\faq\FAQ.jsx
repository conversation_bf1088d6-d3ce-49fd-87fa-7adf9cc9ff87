import FaqAccordion from "../../pages/componets/FaqAccordion.jsx";
import {useFaq} from "../../hooks/faq/useFaq.js";
import Spinner from "../common/Spinner.jsx";
import {Link} from "react-router-dom";
import { useTranslation } from "../../hooks/translation/useTranslation";
import { useTranslatedFAQ } from "../../hooks/translation/useTranslatedData";

export default function FAQ(){
    const { t } = useTranslation();
    const {faq, faqLoading} = useFaq();
    const { data: translatedFaq, isTranslating } = useTranslatedFAQ(faq);

    if(faqLoading || !faq) return <Spinner />;

    return (
        <div className="col mb-5">
            <div className="col d-flex gap-3 justify-content-between align-items-center mb-4">
                <p className="vb_title_sm">{t('faq.title')}</p>
                <p className="vb_faq_head text-end">
                    {t('faq.needHelp')} <br className="d-md-none"/> {t('faq.visitHelpCenter')}{" "}
                    <Link to="/help-center"><u>{t('help.title')}</u></Link>
                </p>
            </div>

            <div className="col">
                {isTranslating && <div className="text-center mb-3"><Spinner /></div>}
                {(translatedFaq || []).map(item=> (
                    <FaqAccordion
                        key={item._id}
                        isActive={false}
                        triggerText={item.title}
                        contentText={item.content}
                    />
                ))}
            </div>
        </div>
    )
}